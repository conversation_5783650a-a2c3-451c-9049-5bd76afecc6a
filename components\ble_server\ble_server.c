#include "ble_server.h"
#include "ble_gap.h"
#include "ble_gatt_svc.h"
#include "host/ble_hs.h"
#include "host/ble_uuid.h"
#include "host/util/util.h"
#include "nimble/ble.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include <stdlib.h>
#include <time.h>

static const char *TAG = BLE_SERVER_TAG;

// NimBLE主机任务
static void nimble_host_task(void *param)
{
    ESP_LOGI(TAG, "NimBLE host task started");
    
    // 运行NimBLE主机
    nimble_port_run();
    
    // 清理
    vTaskDelete(NULL);
}

// 栈重置回调
static void on_stack_reset(int reason)
{
    ESP_LOGI(TAG, "NimBLE stack reset, reason: %d", reason);
}

// 栈同步回调
static void on_stack_sync(void)
{
    ESP_LOGI(TAG, "NimBLE stack synced");
    
    // 开始广播
    ble_gap_start_advertising();
}

// NimBLE主机配置初始化
static void nimble_host_config_init(void)
{
    // 设置主机回调
    ble_hs_cfg.reset_cb = on_stack_reset;
    ble_hs_cfg.sync_cb = on_stack_sync;
    ble_hs_cfg.gatts_register_cb = ble_gatt_register_cb;
    ble_hs_cfg.store_status_cb = ble_store_util_status_rr;
    
    // 存储配置初始化
    ble_store_config_init();
}

esp_err_t ble_server_init(void)
{
    esp_err_t ret;
    int rc;
    
    ESP_LOGI(TAG, "Initializing BLE server");
    
    // 初始化NimBLE端口
    ret = nimble_port_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize NimBLE port: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 初始化GAP服务
    rc = ble_gap_init();
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to initialize GAP service: %d", rc);
        return ESP_FAIL;
    }
    
    // 初始化GATT服务
    rc = ble_gatt_svc_init();
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to initialize GATT service: %d", rc);
        return ESP_FAIL;
    }
    
    // 配置NimBLE主机
    nimble_host_config_init();
    
    ESP_LOGI(TAG, "BLE server initialized successfully");
    return ESP_OK;
}

esp_err_t ble_server_start(void)
{
    ESP_LOGI(TAG, "Starting BLE server");
    
    // 创建NimBLE主机任务
    xTaskCreate(nimble_host_task, "NimBLE Host", 4*1024, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "BLE server started");
    return ESP_OK;
}

esp_err_t ble_server_stop(void)
{
    ESP_LOGI(TAG, "Stopping BLE server");
    
    // 停止NimBLE端口
    nimble_port_stop();
    
    ESP_LOGI(TAG, "BLE server stopped");
    return ESP_OK;
}

uint16_t get_random_heart_rate(void)
{
    // 生成60-100之间的随机心率值
    return 60 + (rand() % 41);
}
