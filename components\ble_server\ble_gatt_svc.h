#ifndef BLE_GATT_SVC_H
#define BLE_GATT_SVC_H

#include "host/ble_gatt.h"
#include "host/ble_gap.h"

#ifdef __cplusplus
extern "C" {
#endif

// GATT服务初始化
int ble_gatt_svc_init(void);

// GATT注册回调
void ble_gatt_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg);

// 订阅回调
void ble_gatt_subscribe_cb(struct ble_gap_event *event);

// 心率特征访问回调
int heart_rate_chr_access(uint16_t conn_handle, uint16_t attr_handle,
                         struct ble_gatt_access_ctxt *ctxt, void *arg);

// PrintMe特征访问回调
int printme_chr_access(uint16_t conn_handle, uint16_t attr_handle,
                      struct ble_gatt_access_ctxt *ctxt, void *arg);

#ifdef __cplusplus
}
#endif

#endif // BLE_GATT_SVC_H
