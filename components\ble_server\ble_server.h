#ifndef BLE_SERVER_H
#define BLE_SERVER_H

#include "esp_err.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"

#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "host/util/util.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"

#ifdef __cplusplus
extern "C" {
#endif

#define BLE_SERVER_TAG "BLE_SERVER"

// BLE服务器初始化
esp_err_t ble_server_init(void);

// BLE服务器启动
esp_err_t ble_server_start(void);

// BLE服务器停止
esp_err_t ble_server_stop(void);

// 获取随机心率值
uint16_t get_random_heart_rate(void);

#ifdef __cplusplus
}
#endif

#endif // BLE_SERVER_H
