#include "ble_gatt_svc.h"
#include "ble_server.h"
#include <string.h>

static const char *TAG = BLE_SERVER_TAG;

// 心率服务UUID (标准心率服务)
static const ble_uuid16_t heart_rate_svc_uuid = BLE_UUID16_INIT(0x180D);

// 心率测量特征UUID
static const ble_uuid16_t heart_rate_chr_uuid = BLE_UUID16_INIT(0x2A37);
static uint16_t heart_rate_chr_val_handle;

// 自定义PrintMe服务UUID
static const ble_uuid128_t printme_svc_uuid = 
    BLE_UUID128_INIT(0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
                     0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0);

// PrintMe特征UUID
static const ble_uuid128_t printme_chr_uuid = 
    BLE_UUID128_INIT(0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf1,
                     0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf1);
static uint16_t printme_chr_val_handle;

// 心率数据格式 (标志位 + 心率值)
static uint8_t heart_rate_data[2] = {0x00, 0x00};

// GATT服务表定义
static const struct ble_gatt_svc_def gatt_svr_svcs[] = {
    // 心率服务
    {
        .type = BLE_GATT_SVC_TYPE_PRIMARY,
        .uuid = &heart_rate_svc_uuid.u,
        .characteristics = (struct ble_gatt_chr_def[]) {
            {
                // 心率测量特征
                .uuid = &heart_rate_chr_uuid.u,
                .access_cb = heart_rate_chr_access,
                .flags = BLE_GATT_CHR_F_READ,
                .val_handle = &heart_rate_chr_val_handle,
            },
            {
                0, // 特征结束标记
            }
        },
    },
    
    // PrintMe服务
    {
        .type = BLE_GATT_SVC_TYPE_PRIMARY,
        .uuid = &printme_svc_uuid.u,
        .characteristics = (struct ble_gatt_chr_def[]) {
            {
                // PrintMe特征
                .uuid = &printme_chr_uuid.u,
                .access_cb = printme_chr_access,
                .flags = BLE_GATT_CHR_F_WRITE,
                .val_handle = &printme_chr_val_handle,
            },
            {
                0, // 特征结束标记
            }
        },
    },
    
    {
        0, // 服务结束标记
    },
};

// 心率特征访问回调
int heart_rate_chr_access(uint16_t conn_handle, uint16_t attr_handle,
                         struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    int rc;
    
    switch (ctxt->op) {
    case BLE_GATT_ACCESS_OP_READ_CHR:
        ESP_LOGI(TAG, "Heart rate characteristic read; conn_handle=%d attr_handle=%d",
                conn_handle, attr_handle);
        
        if (attr_handle == heart_rate_chr_val_handle) {
            // 获取随机心率值
            uint16_t heart_rate = get_random_heart_rate();
            heart_rate_data[1] = (uint8_t)heart_rate;
            
            ESP_LOGI(TAG, "Returning heart rate: %d bpm", heart_rate);
            
            // 将心率数据复制到响应缓冲区
            rc = os_mbuf_append(ctxt->om, heart_rate_data, sizeof(heart_rate_data));
            return rc == 0 ? 0 : BLE_ATT_ERR_INSUFFICIENT_RES;
        }
        goto error;
        
    default:
        goto error;
    }
    
error:
    ESP_LOGE(TAG, "Unexpected access operation to heart rate characteristic, opcode: %d", ctxt->op);
    return BLE_ATT_ERR_UNLIKELY;
}

// PrintMe特征访问回调
int printme_chr_access(uint16_t conn_handle, uint16_t attr_handle,
                      struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    switch (ctxt->op) {
    case BLE_GATT_ACCESS_OP_WRITE_CHR:
        ESP_LOGI(TAG, "PrintMe characteristic write; conn_handle=%d attr_handle=%d",
                conn_handle, attr_handle);
        
        if (attr_handle == printme_chr_val_handle) {
            // 获取写入的数据长度
            uint16_t data_len = OS_MBUF_PKTLEN(ctxt->om);
            
            if (data_len > 0) {
                // 分配缓冲区来存储接收的数据
                char *received_data = malloc(data_len + 1);
                if (received_data != NULL) {
                    // 复制数据
                    os_mbuf_copydata(ctxt->om, 0, data_len, received_data);
                    received_data[data_len] = '\0'; // 添加字符串结束符
                    
                    // 打印接收到的数据
                    ESP_LOGI(TAG, "PrintMe received: %s", received_data);
                    
                    // 释放缓冲区
                    free(received_data);
                } else {
                    ESP_LOGE(TAG, "Failed to allocate memory for received data");
                }
            } else {
                ESP_LOGI(TAG, "PrintMe received empty data");
            }
            
            return 0;
        }
        goto error;
        
    default:
        goto error;
    }
    
error:
    ESP_LOGE(TAG, "Unexpected access operation to PrintMe characteristic, opcode: %d", ctxt->op);
    return BLE_ATT_ERR_UNLIKELY;
}

// GATT注册回调
void ble_gatt_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg)
{
    char buf[BLE_UUID_STR_LEN];
    
    switch (ctxt->op) {
    case BLE_GATT_REGISTER_OP_SVC:
        ESP_LOGI(TAG, "Registered service %s with handle=%d",
                ble_uuid_to_str(ctxt->svc.svc_def->uuid, buf),
                ctxt->svc.handle);
        break;
        
    case BLE_GATT_REGISTER_OP_CHR:
        ESP_LOGI(TAG, "Registered characteristic %s with def_handle=%d val_handle=%d",
                ble_uuid_to_str(ctxt->chr.chr_def->uuid, buf),
                ctxt->chr.def_handle,
                ctxt->chr.val_handle);
        break;
        
    case BLE_GATT_REGISTER_OP_DSC:
        ESP_LOGI(TAG, "Registered descriptor %s with handle=%d",
                ble_uuid_to_str(ctxt->dsc.dsc_def->uuid, buf),
                ctxt->dsc.handle);
        break;
        
    default:
        assert(0);
        break;
    }
}

// 订阅回调
void ble_gatt_subscribe_cb(struct ble_gap_event *event)
{
    ESP_LOGI(TAG, "Subscribe event; conn_handle=%d attr_handle=%d",
            event->subscribe.conn_handle, event->subscribe.attr_handle);
}

// GATT服务初始化
int ble_gatt_svc_init(void)
{
    int rc;
    
    // 初始化GATT服务
    ble_svc_gatt_init();
    
    // 计算服务配置
    rc = ble_gatts_count_cfg(gatt_svr_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to count GATT services; rc=%d", rc);
        return rc;
    }
    
    // 添加服务
    rc = ble_gatts_add_svcs(gatt_svr_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to add GATT services; rc=%d", rc);
        return rc;
    }
    
    ESP_LOGI(TAG, "GATT services initialized");
    return 0;
}
